@echo off
setlocal enabledelayedexpansion

REM 设置初始端口和网站目录
set "port=39876"
set "webdir=www"

REM 检查端口占用并找到可用端口
:port_loop
netstat -ano | findstr ":%port% " > nul
if %errorlevel% equ 0 (
    set /a port+=1
    goto :port_loop
)

REM 创建临时VBS脚本作为Web服务器
set "vbsfile=%temp%\miniserver.vbs"
(
echo Set http = CreateObject("MSXML2.ServerXMLHTTP")
echo If http Is Nothing Then Set http = CreateObject("Microsoft.XMLHTTP")
echo If http Is Nothing Then 
echo   WScript.Echo "Failed to create XMLHTTP object."
echo   WScript.Quit 1
echo End If
echo 
echo url = "http://127.0.0.1:" ^& %port% ^& "/"
echo http.open "GET", url, False
echo http.send
echo If http.status = 200 Then 
echo   WScript.Echo "Server is already running."
echo   WScript.Quit 1
echo End If
) > "%vbsfile%"

REM 创建主服务器脚本
set "servervbs=%temp%\server_%port%.vbs"
(
echo Set FSO = CreateObject("Scripting.FileSystemObject")
echo Set WshShell = WScript.CreateObject("WScript.Shell")
echo port = %port%
echo webdir = "%~dp0%webdir%"
echo 
echo Function GetContentType(filename)
echo   ext = LCase(FSO.GetExtensionName(filename))
echo   Select Case ext
echo     Case "html", "htm": GetContentType = "text/html"
echo     Case "txt":         GetContentType = "text/plain"
echo     Case "css":         GetContentType = "text/css"
echo     Case "js":          GetContentType = "application/javascript"
echo     Case "jpg", "jpeg": GetContentType = "image/jpeg"
echo     Case "gif":         GetContentType = "image/gif"
echo     Case "png":         GetContentType = "image/png"
echo     Case Else:          GetContentType = "application/octet-stream"
echo   End Select
echo End Function
echo 
echo On Error Resume Next
echo Set server = CreateObject("Winsock.Winsock")
echo If Err.Number <> 0 Then
echo   WScript.Echo "Error creating Winsock object: " ^& Err.Description
echo   WScript.Quit 1
echo End If
echo 
echo server.LocalPort = port
echo server.Listen
echo WshShell.Run "cmd /c start """" ""http://127.0.0.1:" ^& port ^& """", 0, False
echo WScript.Echo "Web server started at http://127.0.0.1:" ^& port
echo 
echo Do While True
echo   server.WaitForConnection 1000
echo   If server.BytesReceived > 0 Then
echo     data = server.GetData()
echo     request = Split(data, vbCrLf)(0)
echo     path = Split(request, " ")(1)
echo     If path = "/" Then path = "/index.html"
echo     
echo     filePath = FSO.BuildPath(webdir, Mid(path, 2))
echo     
echo     If FSO.FileExists(filePath) Then
echo       Set stream = CreateObject("ADODB.Stream")
echo       stream.Type = 1
echo       stream.Open
echo       stream.LoadFromFile filePath
echo       content = stream.Read
echo       stream.Close
echo       
echo       ctype = GetContentType(filePath)
echo       response = "HTTP/1.1 200 OK" ^& vbCrLf ^& _
echo                  "Content-Type: " ^& ctype ^& vbCrLf ^& _
echo                  "Connection: close" ^& vbCrLf ^& vbCrLf
echo       server.SendData response
echo       server.SendData content
echo     Else
echo       response = "HTTP/1.1 404 Not Found" ^& vbCrLf ^& _
echo                  "Content-Type: text/html" ^& vbCrLf ^& vbCrLf ^& _
echo                  "<html><body><h1>404 Not Found</h1></body></html>"
echo       server.SendData response
echo     End If
echo     server.Disconnect
echo   End If
echo Loop
) > "%servervbs%"

REM 启动Web服务器
start "WebServer" /min wscript.exe "%servervbs%"

REM 等待服务器启动
timeout /t 3 /nobreak >nul

echo Web server started on http://127.0.0.1:%port%
echo Press any key to stop the server...
pause >nul

REM 清理
taskkill /f /im wscript.exe >nul 2>&1
del "%servervbs%" >nul
del "%vbsfile%" >nul
endlocal